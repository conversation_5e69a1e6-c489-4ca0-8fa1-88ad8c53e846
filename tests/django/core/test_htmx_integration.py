"""
Comprehensive HTMX Integration Tests for Core App

This module tests the core HTMX functionality including dashboard updates,
search functionality, navigation components, and real-time features.
Tests are designed for HTMX 2.0.3 with Django 5.2+ patterns.
"""

import json
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.test import override_settings
from django.urls import reverse

from apps.authentication.models import Organization
from apps.projects.models import Project
from tests.utils.htmx_test_base import (
    HTMXAccessibilityTestMixin,
    HTMXIntegrationTestMixin,
    HTMXPerformanceTestMixin,
    HTMXTestCase,
)

User = get_user_model()


class CoreHTMXDashboardTest(HTMXTestCase, HTMXPerformanceTestMixin, HTMXAccessibilityTestMixin):
    """Test HTMX dashboard functionality in core app."""

    def setUp(self):
        super().setUp()

        # Create test projects for dashboard stats
        self.projects = []
        for i in range(3):
            project = Project.objects.create(
                name=f"Test Project {i + 1}",
                description=f"Description for project {i + 1}",
                organization=self.organization,
                created_by=self.user,
                is_active=True,
            )
            self.projects.append(project)

    def test_dashboard_stats_htmx_endpoint(self):
        """Test HTMX dashboard stats endpoint."""
        url = reverse("htmx:dashboard_stats")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Verify content structure
        content = self.get_htmx_content(response)

        # Should contain stats cards
        stats_cards = content.find_all(class_="stats-card")
        self.assertGreater(len(stats_cards), 0, "Dashboard should contain stats cards")

        # Should contain project count
        self.assertIn("3", response.content.decode(), "Should show project count")

    def test_dashboard_stats_performance(self):
        """Test dashboard stats endpoint performance."""
        url = reverse("htmx:dashboard_stats")

        metrics = self.assert_htmx_performance(url, max_avg_time=1.0)

        # Dashboard stats should be fast
        self.assertLess(metrics["avg_time"], 0.5, "Dashboard stats should load quickly")

    def test_dashboard_stats_accessibility(self):
        """Test dashboard stats accessibility."""
        url = reverse("htmx:dashboard_stats")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_accessibility(response)

    def test_dashboard_recent_activity_htmx(self):
        """Test HTMX recent activity endpoint."""
        url = reverse("htmx:recent_activity")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Verify activity structure
        content = self.get_htmx_content(response)
        activity_list = content.find(class_="activity-list")
        self.assertIsNotNone(activity_list, "Should contain activity list")

    def test_dashboard_stats_cards_htmx(self):
        """Test HTMX stats cards endpoint."""
        url = reverse("htmx:dashboard_stats_cards")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain multiple stat cards
        content = self.get_htmx_content(response)
        cards = content.find_all(class_="card")
        self.assertGreater(len(cards), 0, "Should contain stat cards")

    def test_dashboard_layout_save_htmx(self):
        """Test HTMX dashboard layout saving."""
        url = reverse("htmx:save_dashboard_layout")

        layout_data = {
            "layout": json.dumps(
                [{"id": "stats", "x": 0, "y": 0, "w": 6, "h": 2}, {"id": "activity", "x": 6, "y": 0, "w": 6, "h": 4}]
            )
        }

        response = self.htmx_client.htmx_post(url, layout_data)

        self.assert_htmx_response(response, status_code=200)

        # Should return success response
        content = response.content.decode()
        self.assertIn("success", content.lower())

    def test_dashboard_layout_load_htmx(self):
        """Test HTMX dashboard layout loading."""
        url = reverse("htmx:load_dashboard_layout")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)

    def test_dashboard_layout_reset_htmx(self):
        """Test HTMX dashboard layout reset."""
        url = reverse("htmx:reset_dashboard_layout")

        response = self.htmx_client.htmx_post(url)

        self.assert_htmx_response(response, status_code=200)


class CoreHTMXSearchTest(HTMXTestCase, HTMXPerformanceTestMixin, HTMXIntegrationTestMixin):
    """Test HTMX search functionality in core app."""

    def setUp(self):
        super().setUp()

        # Create searchable content
        self.projects = []
        for i in range(5):
            project = Project.objects.create(
                name=f"Searchable Project {i + 1}",
                description=f"This is a test project for search functionality {i + 1}",
                organization=self.organization,
                created_by=self.user,
                is_active=True,
            )
            self.projects.append(project)

    def test_global_search_htmx(self):
        """Test HTMX global search endpoint."""
        url = reverse("htmx:global_search")

        search_data = {"q": "Searchable"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain search results
        content = self.get_htmx_content(response)
        results = content.find_all(class_="search-result")
        self.assertGreater(len(results), 0, "Should return search results")

    def test_search_autocomplete_htmx(self):
        """Test HTMX search autocomplete."""
        url = reverse("htmx:search_autocomplete")

        search_data = {"q": "Search"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain autocomplete suggestions
        content = self.get_htmx_content(response)
        suggestions = content.find_all(class_="autocomplete-item")
        self.assertGreaterEqual(len(suggestions), 0, "Should return autocomplete suggestions")

    def test_search_results_htmx(self):
        """Test HTMX search results endpoint."""
        url = reverse("htmx:search_results")

        search_data = {"q": "Project", "type": "project"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain paginated results
        content = self.get_htmx_content(response)
        self.assertIsNotNone(content.find(class_="search-results"))

    def test_projects_search_htmx(self):
        """Test HTMX project-specific search."""
        url = reverse("htmx:projects_search")

        search_data = {"q": "Searchable"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain project results
        content = response.content.decode()
        self.assertIn("Searchable Project", content)

    def test_search_toggle_advanced_htmx(self):
        """Test HTMX advanced search toggle."""
        url = reverse("htmx:toggle_advanced_search")

        response = self.htmx_client.htmx_post(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should toggle advanced search form
        content = self.get_htmx_content(response)
        advanced_form = content.find(class_="advanced-search-form")
        self.assertIsNotNone(advanced_form, "Should show advanced search form")

    def test_search_clear_filters_htmx(self):
        """Test HTMX search filter clearing."""
        url = reverse("htmx:clear_search_filters")

        response = self.htmx_client.htmx_post(url)

        self.assert_htmx_response(response, status_code=200)

    def test_search_tips_htmx(self):
        """Test HTMX search tips endpoint."""
        url = reverse("htmx:search_tips")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain search tips
        content = self.get_htmx_content(response)
        tips = content.find_all(class_="search-tip")
        self.assertGreater(len(tips), 0, "Should contain search tips")

    def test_saved_searches_htmx(self):
        """Test HTMX saved searches endpoint."""
        url = reverse("htmx:saved_searches")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_search_performance(self):
        """Test search endpoint performance."""
        url = reverse("htmx:global_search")

        search_data = {"q": "test"}

        metrics = self.measure_htmx_response_time(url, data=search_data)

        # Search should be reasonably fast
        self.assertLess(metrics["avg_time"], 2.0, "Search should complete quickly")

    def test_search_user_interaction_flow(self):
        """Test complete search user interaction flow."""
        steps = [
            {
                "action": "get",
                "url": reverse("htmx:search_autocomplete"),
                "data": {"q": "Proj"},
                "assertions": [lambda r: self.assertEqual(r.status_code, 200), lambda r: self.assert_htmx_partial(r)],
            },
            {
                "action": "get",
                "url": reverse("htmx:global_search"),
                "data": {"q": "Project"},
                "assertions": [
                    lambda r: self.assertEqual(r.status_code, 200),
                    lambda r: self.assertIn("Project", r.content.decode()),
                ],
            },
            {
                "action": "post",
                "url": reverse("htmx:toggle_advanced_search"),
                "assertions": [lambda r: self.assertEqual(r.status_code, 200)],
            },
        ]

        responses = self.simulate_htmx_user_interaction(steps)
        self.assertEqual(len(responses), 3)


class CoreHTMXUtilityTest(HTMXTestCase):
    """Test HTMX utility endpoints in core app."""

    def test_toast_htmx(self):
        """Test HTMX toast notification endpoint."""
        url = reverse("htmx:toast")

        toast_data = {"message": "Test toast message", "type": "success"}

        response = self.htmx_client.htmx_post(url, toast_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain toast message
        content = response.content.decode()
        self.assertIn("Test toast message", content)
        self.assertIn("success", content)

    def test_loading_state_htmx(self):
        """Test HTMX loading state endpoint."""
        url = reverse("htmx:loading_state")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain loading indicator
        content = self.get_htmx_content(response)
        loading_indicator = content.find(class_="loading-indicator")
        self.assertIsNotNone(loading_indicator, "Should contain loading indicator")

    def test_error_recovery_htmx(self):
        """Test HTMX error recovery endpoint."""
        url = reverse("htmx:error_recovery")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain error recovery UI
        content = self.get_htmx_content(response)
        error_recovery = content.find(class_="error-recovery")
        self.assertIsNotNone(error_recovery, "Should contain error recovery UI")

    def test_retry_action_htmx(self):
        """Test HTMX retry action endpoint."""
        url = reverse("htmx:retry_action")

        retry_data = {"action": "test_action"}

        response = self.htmx_client.htmx_post(url, retry_data)

        self.assert_htmx_response(response, status_code=200)

    def test_user_autocomplete_htmx(self):
        """Test HTMX user autocomplete endpoint."""
        url = reverse("htmx:user_autocomplete")

        # Create additional users for autocomplete
        self.create_additional_user("alice", first_name="Alice", last_name="Smith")
        self.create_additional_user("bob", first_name="Bob", last_name="Jones")

        search_data = {"q": "alice"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain user suggestions
        content = response.content.decode()
        self.assertIn("Alice", content)

    def test_location_autocomplete_htmx(self):
        """Test HTMX location autocomplete endpoint."""
        url = reverse("htmx:location_autocomplete")

        search_data = {"q": "New York"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)


class CoreHTMXModalFormTest(HTMXTestCase):
    """Test HTMX modal and form functionality."""

    def test_enhanced_modal_view(self):
        """Test enhanced modal view functionality."""
        url = reverse("htmx:modal", kwargs={"modal_name": "test_modal"})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain modal structure
        content = self.get_htmx_content(response)
        modal = content.find(class_="modal")
        self.assertIsNotNone(modal, "Should contain modal structure")

    def test_enhanced_form_view(self):
        """Test enhanced form view functionality."""
        url = reverse("htmx:form", kwargs={"form_name": "test_form"})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain form structure
        content = self.get_htmx_content(response)
        form = content.find("form")
        self.assertIsNotNone(form, "Should contain form element")

    def test_enhanced_partial_view(self):
        """Test enhanced partial view functionality."""
        url = reverse("htmx:partial", kwargs={"partial_name": "test_partial"})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_enhanced_infinite_scroll_view(self):
        """Test enhanced infinite scroll view."""
        url = reverse("htmx:infinite_scroll")

        scroll_data = {"page": 1, "limit": 10}

        response = self.htmx_client.htmx_get(url, scroll_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain scrollable items
        content = self.get_htmx_content(response)
        items = content.find_all(attrs={"data-scroll-item": True})
        self.assertGreaterEqual(len(items), 0, "Should contain scroll items")


class CoreHTMXErrorHandlingTest(HTMXTestCase):
    """Test HTMX error handling."""

    def test_htmx_404_handling(self):
        """Test HTMX 404 error handling."""
        url = "/htmx/nonexistent-endpoint/"

        response = self.htmx_client.htmx_get(url)

        self.assertEqual(response.status_code, 404)

    def test_htmx_403_handling(self):
        """Test HTMX 403 error handling with insufficient permissions."""
        # Create a restricted endpoint test would require actual restricted view
        pass

    def test_htmx_500_handling(self):
        """Test HTMX 500 error handling."""
        # Would require mocking a view to raise an exception
        with patch("apps.core.views.htmx_views.dashboard_stats") as mock_view:
            mock_view.side_effect = Exception("Test exception")

            url = reverse("htmx:dashboard_stats")
            response = self.htmx_client.htmx_get(url)

            # Django should handle the exception gracefully
            self.assertIn(response.status_code, [500, 200])

    def test_htmx_csrf_protection(self):
        """Test HTMX CSRF protection on POST requests."""
        url = reverse("htmx:save_dashboard_layout")

        # Remove CSRF token
        self.htmx_client.cookies.clear()

        layout_data = {"layout": "{}"}

        response = self.htmx_client.htmx_post(url, layout_data)

        # Should be rejected due to CSRF
        self.assertIn(response.status_code, [403, 302])


class CoreHTMXOrganizationIsolationTest(HTMXTestCase):
    """Test HTMX organization isolation."""

    def setUp(self):
        super().setUp()

        # Create second organization and user
        self.other_organization = Organization.objects.create(name="Other Organization", slug="other-org")

        self.other_user = User.objects.create_user(
            username="otheruser", email="<EMAIL>", password="testpass123"
        )

        # Create project in other organization
        self.other_project = Project.objects.create(
            name="Other Organization Project",
            description="Project in other organization",
            organization=self.other_organization,
            created_by=self.other_user,
            is_active=True,
        )

    def test_dashboard_stats_organization_isolation(self):
        """Test dashboard stats respect organization boundaries."""
        url = reverse("htmx:dashboard_stats")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)

        # Should not contain other organization's data
        content = response.content.decode()
        self.assertNotIn("Other Organization Project", content)

    def test_search_organization_isolation(self):
        """Test search respects organization boundaries."""
        url = reverse("htmx:global_search")

        search_data = {"q": "Organization"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)

        # Should not return results from other organization
        content = response.content.decode()
        self.assertNotIn("Other Organization Project", content)


@override_settings(
    CACHES={
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        }
    }
)
class CoreHTMXCachingTest(HTMXTestCase):
    """Test HTMX caching behavior."""

    def test_dashboard_stats_caching(self):
        """Test dashboard stats caching behavior."""
        url = reverse("htmx:dashboard_stats")

        # First request
        response1 = self.htmx_client.htmx_get(url)
        self.assertEqual(response1.status_code, 200)

        # Second request should potentially use cache
        response2 = self.htmx_client.htmx_get(url)
        self.assertEqual(response2.status_code, 200)

        # Response should be consistent
        self.assertEqual(response1.content, response2.content)

    def test_search_results_no_caching(self):
        """Test search results are not inappropriately cached."""
        url = reverse("htmx:global_search")

        # Search for different terms
        response1 = self.htmx_client.htmx_get(url, {"q": "term1"})
        response2 = self.htmx_client.htmx_get(url, {"q": "term2"})

        # Responses should be different (not cached inappropriately)
        self.assertNotEqual(response1.content, response2.content)


class CoreHTMXDemoTest(HTMXTestCase):
    """Test HTMX demo functionality."""

    def test_htmx_demo_endpoint(self):
        """Test HTMX demo endpoint."""
        url = reverse("htmx:demo")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain demo content
        content = self.get_htmx_content(response)
        self.assertIsNotNone(content.find(), "Demo should contain content")

    def test_demo_progressive_enhancement(self):
        """Test demo works without HTMX."""
        url = reverse("htmx:demo")

        # Request without HTMX headers
        response = self.client.get(url)

        # Should still work (progressive enhancement)
        self.assertIn(response.status_code, [200, 302])
