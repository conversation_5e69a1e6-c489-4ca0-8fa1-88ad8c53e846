"""
Comprehensive HTMX Integration Tests for Analytics App

This module tests the analytics HTMX functionality including dashboard
components, real-time metrics updates, chart data loading, and filtering.
Tests are designed for HTMX 2.0.3 with Django 5.2+ patterns.
"""

import json
from datetime import timedelta
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone

from apps.analytics.models import Activity
from apps.authentication.models import Organization
from apps.financial.models import TimeEntry
from apps.projects.models import Project, Task
from tests.utils.htmx_test_base import (
    HTMXAccessibilityTestMixin,
    HTMXIntegrationTestMixin,
    HTMXPerformanceTestMixin,
    HTMXTestCase,
)

User = get_user_model()


class AnalyticsHTMXDashboardTest(HTMXTestCase, HTMXPerformanceTestMixin, HTMXAccessibilityTestMixin):
    """Test HTMX analytics dashboard functionality."""

    def setUp(self):
        super().setUp()

        # Create test data for analytics
        self.project = Project.objects.create(
            name="Analytics Test Project",
            description="Project for analytics testing",
            organization=self.organization,
            created_by=self.user,
            is_active=True,
        )

        self.task = Task.objects.create(
            title="Test Task",
            description="Task for analytics",
            project=self.project,
            assigned_to=self.user,
            status="in_progress",
            created_by=self.user,
        )

        # Create time entries
        TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            task=self.task,
            start_time=timezone.now() - timedelta(hours=2),
            end_time=timezone.now() - timedelta(hours=1),
            duration=timedelta(hours=1),
            description="Test work",
        )

        # Create activity records
        Activity.objects.create(
            organization=self.organization,
            user=self.user,
            action="project_created",
            object_id=self.project.id,
            content_type_id=1,
            timestamp=timezone.now(),
        )

    def test_dashboard_stats_htmx(self):
        """Test HTMX dashboard stats endpoint."""
        url = reverse("analytics_htmx:dashboard-stats")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain analytics data
        content = self.get_htmx_content(response)
        stats_container = content.find(class_="analytics-stats")
        self.assertIsNotNone(stats_container, "Should contain analytics stats")

    def test_metrics_cards_htmx(self):
        """Test HTMX metrics cards endpoint."""
        url = reverse("analytics_htmx:metrics-cards")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain metric cards
        content = self.get_htmx_content(response)
        metric_cards = content.find_all(class_="metric-card")
        self.assertGreater(len(metric_cards), 0, "Should contain metric cards")

    def test_dashboard_charts_htmx(self):
        """Test HTMX dashboard charts endpoint."""
        url = reverse("analytics_htmx:dashboard-charts")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain chart containers
        content = self.get_htmx_content(response)
        charts = content.find_all(class_="chart-container")
        self.assertGreaterEqual(len(charts), 0, "Should contain chart containers")

    def test_executive_kpi_cards_htmx(self):
        """Test HTMX executive KPI cards endpoint."""
        url = reverse("analytics_htmx:executive-kpis")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain KPI cards for executives
        content = self.get_htmx_content(response)
        kpi_cards = content.find_all(class_="kpi-card")
        self.assertGreaterEqual(len(kpi_cards), 0, "Should contain KPI cards")

    def test_executive_dashboard_content_htmx(self):
        """Test HTMX executive dashboard content endpoint."""
        url = reverse("analytics_htmx:executive-dashboard-content")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_executive_performance_htmx(self):
        """Test HTMX executive performance endpoint."""
        url = reverse("analytics_htmx:executive-performance")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_analytics_performance(self):
        """Test analytics dashboard performance."""
        url = reverse("analytics_htmx:dashboard-stats")

        metrics = self.assert_htmx_performance(url, max_avg_time=2.0)

        # Analytics should be reasonably fast
        self.assertLess(metrics["avg_time"], 1.5, "Analytics dashboard should load quickly")

    def test_analytics_accessibility(self):
        """Test analytics dashboard accessibility."""
        url = reverse("analytics_htmx:metrics-cards")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_accessibility(response)


class AnalyticsHTMXProjectAnalyticsTest(HTMXTestCase):
    """Test HTMX project-specific analytics functionality."""

    def setUp(self):
        super().setUp()

        self.project = Project.objects.create(
            name="Project Analytics Test",
            description="Project for analytics testing",
            organization=self.organization,
            created_by=self.user,
            is_active=True,
        )

    def test_project_analytics_htmx(self):
        """Test HTMX project analytics endpoint."""
        url = reverse("analytics_htmx:project-analytics", kwargs={"project_id": self.project.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain project-specific analytics
        content = response.content.decode()
        self.assertIn(self.project.name, content)

    def test_project_metrics_htmx(self):
        """Test HTMX project metrics endpoint."""
        url = reverse("analytics_htmx:project-metrics", kwargs={"project_id": self.project.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_project_performance_htmx(self):
        """Test HTMX project performance analytics endpoint."""
        url = reverse("analytics_htmx:project-performance", kwargs={"project_id": self.project.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_project_analytics_nonexistent_project(self):
        """Test HTMX project analytics with nonexistent project."""
        import uuid

        fake_uuid = uuid.uuid4()

        url = reverse("analytics_htmx:project-analytics", kwargs={"project_id": fake_uuid})

        response = self.htmx_client.htmx_get(url)

        self.assertEqual(response.status_code, 404)


class AnalyticsHTMXUserAnalyticsTest(HTMXTestCase):
    """Test HTMX user-specific analytics functionality."""

    def setUp(self):
        super().setUp()

        self.other_user = self.create_additional_user("analyticsuser", first_name="Analytics", last_name="User")

    def test_user_analytics_htmx(self):
        """Test HTMX user analytics endpoint."""
        url = reverse("analytics_htmx:user-analytics", kwargs={"user_id": self.user.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain user-specific analytics
        content = response.content.decode()
        self.assertIn(self.user.get_full_name() or self.user.username, content)

    def test_user_activity_htmx(self):
        """Test HTMX user activity endpoint."""
        url = reverse("analytics_htmx:user-activity", kwargs={"user_id": self.user.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_user_performance_htmx(self):
        """Test HTMX user performance analytics endpoint."""
        url = reverse("analytics_htmx:user-performance", kwargs={"user_id": self.user.id})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)


class AnalyticsHTMXOrganizationAnalyticsTest(HTMXTestCase):
    """Test HTMX organization-level analytics functionality."""

    def test_organization_overview_htmx(self):
        """Test HTMX organization overview endpoint."""
        url = reverse("analytics_htmx:organization-overview")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain organization-level data
        content = response.content.decode()
        self.assertIn(self.organization.name, content)

    def test_organization_trends_htmx(self):
        """Test HTMX organization trends endpoint."""
        url = reverse("analytics_htmx:organization-trends")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)


class AnalyticsHTMXChartDataTest(HTMXTestCase):
    """Test HTMX chart data endpoints."""

    def test_chart_data_htmx(self):
        """Test HTMX generic chart data endpoint."""
        url = reverse("analytics_htmx:chart-data")

        chart_params = {"chart_type": "line", "metric": "projects", "period": "week"}

        response = self.htmx_client.htmx_get(url, chart_params)

        self.assert_htmx_response(response, status_code=200)

        # Should return JSON data for charts
        try:
            data = json.loads(response.content)
            self.assertIn("data", data)
        except json.JSONDecodeError:
            # If not JSON, should be HTML partial with chart
            self.assert_htmx_partial(response)

    def test_specific_chart_data_htmx(self):
        """Test HTMX specific chart data endpoint."""
        url = reverse("analytics_htmx:specific-chart-data", kwargs={"chart_type": "line"})

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)

    def test_update_chart_htmx(self):
        """Test HTMX chart update endpoint."""
        url = reverse("analytics_htmx:update-chart", kwargs={"metric_type": "projects"})

        response = self.htmx_client.htmx_post(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_chart_data_with_parameters(self):
        """Test chart data endpoint with various parameters."""
        url = reverse("analytics_htmx:chart-data")

        test_params = [
            {"chart_type": "bar", "period": "month"},
            {"chart_type": "pie", "metric": "tasks"},
            {"chart_type": "line", "period": "year", "metric": "users"},
        ]

        for params in test_params:
            with self.subTest(params=params):
                response = self.htmx_client.htmx_get(url, params)
                self.assert_htmx_response(response, status_code=200)


class AnalyticsHTMXRealtimeTest(HTMXTestCase, HTMXIntegrationTestMixin):
    """Test HTMX real-time analytics updates."""

    def test_realtime_dashboard_update_htmx(self):
        """Test HTMX real-time dashboard update endpoint."""
        url = reverse("analytics_htmx:realtime-dashboard-update")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain updated dashboard content
        content = self.get_htmx_content(response)
        dashboard_content = content.find(class_="dashboard-content")
        self.assertIsNotNone(dashboard_content, "Should contain dashboard content")

    def test_realtime_metrics_update_htmx(self):
        """Test HTMX real-time metrics update endpoint."""
        url = reverse("analytics_htmx:realtime-metrics-update")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_realtime_update_with_trigger(self):
        """Test real-time update with HTMX trigger."""
        url = reverse("analytics_htmx:realtime-dashboard-update")

        response = self.htmx_client.htmx_request_with_trigger(
            "GET", url, trigger_name="data-updated", trigger_element_id="dashboard-stats"
        )

        self.assert_htmx_response(response, status_code=200)

    def test_realtime_update_flow(self):
        """Test complete real-time update flow."""
        steps = [
            {
                "action": "get",
                "url": reverse("analytics_htmx:dashboard-stats"),
                "assertions": [lambda r: self.assertEqual(r.status_code, 200)],
            },
            {
                "action": "get",
                "url": reverse("analytics_htmx:realtime-dashboard-update"),
                "trigger": "refresh-dashboard",
                "assertions": [lambda r: self.assertEqual(r.status_code, 200), lambda r: self.assert_htmx_partial(r)],
            },
        ]

        responses = self.simulate_htmx_user_interaction(steps)
        self.assertEqual(len(responses), 2)


class AnalyticsHTMXFilteringTest(HTMXTestCase):
    """Test HTMX filtering functionality for analytics."""

    def test_filter_by_timeframe_htmx(self):
        """Test HTMX timeframe filtering endpoint."""
        url = reverse("analytics_htmx:filter-timeframe")

        filter_data = {"timeframe": "week"}

        response = self.htmx_client.htmx_post(url, filter_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_filter_by_department_htmx(self):
        """Test HTMX department filtering endpoint."""
        url = reverse("analytics_htmx:filter-department")

        filter_data = {"department": "engineering"}

        response = self.htmx_client.htmx_post(url, filter_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_search_analytics_htmx(self):
        """Test HTMX analytics search endpoint."""
        url = reverse("analytics_htmx:search-analytics")

        search_data = {"q": "project"}

        response = self.htmx_client.htmx_get(url, search_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

    def test_multiple_filter_application(self):
        """Test applying multiple filters in sequence."""
        base_stats_url = reverse("analytics_htmx:dashboard-stats")
        timeframe_url = reverse("analytics_htmx:filter-timeframe")
        department_url = reverse("analytics_htmx:filter-department")

        # Get initial stats
        response1 = self.htmx_client.htmx_get(base_stats_url)
        self.assertEqual(response1.status_code, 200)

        # Apply timeframe filter
        response2 = self.htmx_client.htmx_post(timeframe_url, {"timeframe": "month"})
        self.assertEqual(response2.status_code, 200)

        # Apply department filter
        response3 = self.htmx_client.htmx_post(department_url, {"department": "engineering"})
        self.assertEqual(response3.status_code, 200)


class AnalyticsHTMXExportSharingTest(HTMXTestCase):
    """Test HTMX export and sharing functionality."""

    def test_export_preview_htmx(self):
        """Test HTMX export preview endpoint."""
        url = reverse("analytics_htmx:export-preview")

        export_data = {"format": "pdf", "charts": ["projects", "tasks", "users"]}

        response = self.htmx_client.htmx_post(url, export_data)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain export preview
        content = self.get_htmx_content(response)
        export_preview = content.find(class_="export-preview")
        self.assertIsNotNone(export_preview, "Should contain export preview")

    def test_share_dashboard_htmx(self):
        """Test HTMX dashboard sharing endpoint."""
        url = reverse("analytics_htmx:share-dashboard")

        share_data = {"email": "<EMAIL>", "message": "Check out this dashboard"}

        response = self.htmx_client.htmx_post(url, share_data)

        self.assert_htmx_response(response, status_code=200)

    def test_export_formats(self):
        """Test different export formats."""
        url = reverse("analytics_htmx:export-preview")

        formats = ["pdf", "excel", "csv", "png"]

        for format_type in formats:
            with self.subTest(format=format_type):
                export_data = {"format": format_type}
                response = self.htmx_client.htmx_post(url, export_data)
                self.assert_htmx_response(response, status_code=200)


class AnalyticsHTMXNotificationsTest(HTMXTestCase):
    """Test HTMX analytics notifications and alerts."""

    def test_analytics_alerts_htmx(self):
        """Test HTMX analytics alerts endpoint."""
        url = reverse("analytics_htmx:analytics-alerts")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)

        # Should contain alerts container
        content = self.get_htmx_content(response)
        alerts_container = content.find(class_="alerts-container")
        self.assertIsNotNone(alerts_container, "Should contain alerts container")

    def test_performance_notifications_htmx(self):
        """Test HTMX performance notifications endpoint."""
        url = reverse("analytics_htmx:performance-notifications")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)
        self.assert_htmx_partial(response)


class AnalyticsHTMXErrorHandlingTest(HTMXTestCase):
    """Test HTMX error handling in analytics."""

    def test_analytics_error_recovery(self):
        """Test analytics error recovery patterns."""
        # Test with invalid chart type
        url = reverse("analytics_htmx:specific-chart-data", kwargs={"chart_type": "invalid"})

        response = self.htmx_client.htmx_get(url)

        # Should handle gracefully
        self.assertIn(response.status_code, [400, 404, 200])

    def test_analytics_timeout_handling(self):
        """Test handling of analytics timeouts."""
        with patch("apps.analytics.services.analytics.AnalyticsService.get_dashboard_stats") as mock_service:
            mock_service.side_effect = TimeoutError("Analytics timeout")

            url = reverse("analytics_htmx:dashboard-stats")
            response = self.htmx_client.htmx_get(url)

            # Should handle timeout gracefully
            self.assertIn(response.status_code, [200, 500, 503])

    def test_analytics_permission_errors(self):
        """Test analytics permission error handling."""
        # Create user without analytics permissions
        limited_user = self.create_additional_user("limited")

        # Create new client with limited user
        limited_client = self.htmx_client.__class__()
        limited_client.force_login(limited_user)

        url = reverse("analytics_htmx:executive-kpis")
        response = limited_client.htmx_get(url)

        # Should handle permission error
        self.assertIn(response.status_code, [403, 302])


class AnalyticsHTMXOrganizationIsolationTest(HTMXTestCase):
    """Test analytics organization isolation."""

    def setUp(self):
        super().setUp()

        # Create second organization
        self.other_organization = Organization.objects.create(name="Other Analytics Org", slug="other-analytics-org")

        self.other_user = User.objects.create_user(
            username="otheranalyticsuser", email="<EMAIL>", password="testpass123"
        )

        # Create project in other organization
        self.other_project = Project.objects.create(
            name="Other Org Analytics Project",
            description="Project in other organization",
            organization=self.other_organization,
            created_by=self.other_user,
            is_active=True,
        )

    def test_dashboard_stats_isolation(self):
        """Test dashboard stats organization isolation."""
        url = reverse("analytics_htmx:dashboard-stats")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)

        # Should not contain other organization's data
        content = response.content.decode()
        self.assertNotIn("Other Org Analytics Project", content)

    def test_organization_overview_isolation(self):
        """Test organization overview isolation."""
        url = reverse("analytics_htmx:organization-overview")

        response = self.htmx_client.htmx_get(url)

        self.assert_htmx_response(response, status_code=200)

        # Should only show current organization data
        content = response.content.decode()
        self.assertIn(self.organization.name, content)
        self.assertNotIn(self.other_organization.name, content)


class AnalyticsHTMXCachingPerformanceTest(HTMXTestCase):
    """Test analytics caching and performance optimizations."""

    def test_dashboard_stats_caching(self):
        """Test dashboard stats caching behavior."""
        url = reverse("analytics_htmx:dashboard-stats")

        # First request
        response1 = self.htmx_client.htmx_get(url)
        self.assertEqual(response1.status_code, 200)

        # Second request should potentially use cache
        response2 = self.htmx_client.htmx_get(url)
        self.assertEqual(response2.status_code, 200)

    def test_chart_data_performance(self):
        """Test chart data endpoint performance."""
        url = reverse("analytics_htmx:chart-data")

        # Test with different chart types
        chart_types = ["line", "bar", "pie"]

        for chart_type in chart_types:
            with self.subTest(chart_type=chart_type):
                params = {"chart_type": chart_type}

                start_time = timezone.now()
                response = self.htmx_client.htmx_get(url, params)
                end_time = timezone.now()

                self.assertEqual(response.status_code, 200)

                # Should be reasonably fast
                duration = (end_time - start_time).total_seconds()
                self.assertLess(duration, 3.0, f"Chart data for {chart_type} should load quickly")

    @patch("apps.analytics.services.analytics.cache")
    def test_analytics_cache_invalidation(self, mock_cache):
        """Test analytics cache invalidation."""
        mock_cache.get.return_value = None
        mock_cache.set.return_value = True
        mock_cache.delete.return_value = True

        url = reverse("analytics_htmx:dashboard-stats")

        response = self.htmx_client.htmx_get(url)
        self.assertEqual(response.status_code, 200)

        # Cache operations should be called appropriately
        # (This depends on actual caching implementation)
