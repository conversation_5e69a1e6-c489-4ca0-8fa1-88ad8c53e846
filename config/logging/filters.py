"""Custom logging filters for CLEAR HTMX Project.

This module provides specialized logging filters for security monitoring,
performance tracking, and structured logging.
"""

from __future__ import annotations

import logging
import re
import time
from typing import ClassVar


class SecurityLogFilter(logging.Filter):
    """Filter for security-related log records.

    Only allows security-relevant log messages to pass through.
    """

    SECURITY_KEYWORDS: set[str] = {
        "authentication",
        "authorization",
        "login",
        "logout",
        "password",
        "mfa",
        "csrf",
        "xss",
        "sql injection",
        "security",
        "breach",
        "attack",
        "intrusion",
        "threat",
        "vulnerability",
        "exploit",
        "unauthorized",
        "forbidden",
        "blocked",
        "suspicious",
        "malicious",
        "rate limit",
        "firewall",
        "ban",
        "abuse",
        "violation",
    }

    SECURITY_PATHS: set[str] = {
        "/admin/",
        "/auth/",
        "/login/",
        "/logout/",
        "/password/",
        "/mfa/",
        "/security/",
        "/api/auth/",
        "/api/admin/",
    }

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter security-related log records.

        Args:
        ----
            record: Log record to filter

        Returns:
        -------
            True if record should be logged, False otherwise

        """
        # Check if message contains security keywords
        message = getattr(record, "getMessage", lambda: str(record.msg))()
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in self.SECURITY_KEYWORDS):
            return True

        # Check if logger name indicates security relevance
        if "security" in record.name.lower():
            return True

        # Check if record has security-related extra fields
        if hasattr(record, "ip_address") or hasattr(record, "user_id"):
            return True

        # Check if path is security-related
        if hasattr(record, "path"):
            path = getattr(record, "path", "")
            if any(sec_path in path for sec_path in self.SECURITY_PATHS):
                return True

        return False


class PerformanceLogFilter(logging.Filter):
    """Filter for performance-related log records.

    Only allows performance monitoring log messages to pass through.
    """

    PERFORMANCE_KEYWORDS: set[str] = {
        "performance",
        "slow",
        "timeout",
        "latency",
        "response time",
        "query time",
        "cache",
        "memory",
        "cpu",
        "database",
        "connection",
        "pool",
        "optimization",
        "benchmark",
        "profile",
        "metric",
        "monitoring",
        "analytics",
        "statistics",
        "load",
        "throughput",
    }

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter performance-related log records.

        Args:
        ----
            record: Log record to filter

        Returns:
        -------
            True if record should be logged, False otherwise

        """
        # Check if message contains performance keywords
        message = getattr(record, "getMessage", lambda: str(record.msg))()
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in self.PERFORMANCE_KEYWORDS):
            return True

        # Check if logger name indicates performance relevance
        logger_name_lower = record.name.lower()
        if any(keyword in logger_name_lower for keyword in ["performance", "monitor", "metric"]):
            return True

        # Check if record has performance-related extra fields
        performance_fields = [
            "processing_time_ms",
            "response_time",
            "query_time",
            "memory_usage",
            "cpu_usage",
            "connection_count",
            "cache_hit_rate",
            "requests_per_second",
        ]

        return bool(any(hasattr(record, field) for field in performance_fields))


class SensitiveDataFilter(logging.Filter):
    """Filter to remove or mask sensitive data from log records.

    Prevents logging of passwords, tokens, and other sensitive information.
    """

    SENSITIVE_PATTERNS: list[re.Pattern] = [
        re.compile(r'password["\s]*[:=]["\s]*[^"\s]+', re.IGNORECASE),
        re.compile(r'token["\s]*[:=]["\s]*[^"\s]+', re.IGNORECASE),
        re.compile(r'secret["\s]*[:=]["\s]*[^"\s]+', re.IGNORECASE),
        re.compile(r'key["\s]*[:=]["\s]*[^"\s]+', re.IGNORECASE),
        re.compile(r'authorization["\s]*:["\s]*[^"\s]+', re.IGNORECASE),
        re.compile(r'cookie["\s]*:["\s]*[^"\s]+', re.IGNORECASE),
        # Credit card patterns
        re.compile(r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b"),
        # SSN patterns
        re.compile(r"\b\d{3}-\d{2}-\d{4}\b"),
        # Email addresses (partial masking)
        re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"),
    ]

    SENSITIVE_FIELDS: set[str] = {
        "password",
        "token",
        "secret",
        "key",
        "authorization",
        "cookie",
        "session",
        "csrf_token",
        "api_key",
    }

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter and sanitize sensitive data from log records.

        Args:
        ----
            record: Log record to filter

        Returns:
        -------
            True (always allows record through after sanitization)

        """
        # Sanitize the main message
        if hasattr(record, "msg"):
            record.msg = self._sanitize_text(str(record.msg))

        # Sanitize extra fields
        for attr_name in dir(record):
            if attr_name.startswith("_") or attr_name in ["getMessage", "args"]:
                continue

            attr_value = getattr(record, attr_name, None)
            if isinstance(attr_value, str):
                # Check if field name suggests sensitive data
                if attr_name.lower() in self.SENSITIVE_FIELDS:
                    setattr(record, attr_name, self._mask_sensitive_field(attr_value))
                else:
                    setattr(record, attr_name, self._sanitize_text(attr_value))

        return True

    def _sanitize_text(self, text: str) -> str:
        """Sanitize text by removing or masking sensitive patterns.

        Args:
        ----
            text: Text to sanitize

        Returns:
        -------
            Sanitized text

        """
        for pattern in self.SENSITIVE_PATTERNS:
            text = pattern.sub(self._mask_match, text)

        return text

    def _mask_match(self, match: re.Match) -> str:
        """Mask a sensitive data match.

        Args:
        ----
            match: Regex match object

        Returns:
        -------
            Masked replacement string

        """
        matched_text = match.group(0)

        # For key-value patterns, preserve the key and mask the value
        if ":" in matched_text or "=" in matched_text:
            parts = re.split(r"[:=]", matched_text, 1)
            if len(parts) == 2:
                key_part = parts[0]
                return f"{key_part}:***MASKED***"

        # For other patterns, mask most of the content
        if len(matched_text) <= 4:
            return "***"
        if len(matched_text) <= 8:
            return matched_text[:2] + "***"
        return matched_text[:3] + "***" + matched_text[-2:]

    def _mask_sensitive_field(self, value: str) -> str:
        """Completely mask a sensitive field value.

        Args:
        ----
            value: Field value to mask

        Returns:
        -------
            Masked value

        """
        if not value:
            return value

        if len(value) <= 4:
            return "***"
        return f"{value[:2]}***{value[-1:]}"


class RateLimitFilter(logging.Filter):
    """Filter to rate limit log messages and prevent log spam.

    Limits the frequency of similar log messages to prevent overwhelming
    the logging system during high-traffic periods or error conditions.
    """

    def __init__(self, rate_limit: int = 10, time_window: int = 60):
        """Initialize rate limit filter.

        Args:
        ----
            rate_limit: Maximum messages per time window
            time_window: Time window in seconds

        """
        super().__init__()
        self.rate_limit = rate_limit
        self.time_window = time_window
        self.message_counts = {}
        self.last_cleanup = 0

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter log records based on rate limiting.

        Args:
        ----
            record: Log record to filter

        Returns:
        -------
            True if record should be logged, False if rate limited

        """
        # import time (moved to top level)

        current_time = time.time()

        # Cleanup old entries periodically
        if current_time - self.last_cleanup > self.time_window:
            self._cleanup_old_entries(current_time)
            self.last_cleanup = current_time

        # Create message key (combination of logger name and message pattern)
        message_key = self._get_message_key(record)

        # Check current count for this message key
        if message_key not in self.message_counts:
            self.message_counts[message_key] = {"count": 0, "first_seen": current_time}

        message_info = self.message_counts[message_key]

        # Check if within time window
        if current_time - message_info["first_seen"] > self.time_window:
            # Reset counter for new time window
            message_info["count"] = 0
            message_info["first_seen"] = current_time

        # Check rate limit
        if message_info["count"] >= self.rate_limit:
            return False  # Rate limited

        # Allow message and increment counter
        message_info["count"] += 1
        return True

    def _get_message_key(self, record: logging.LogRecord) -> str:
        """Generate a key for rate limiting based on logger and message pattern.

        Args:
        ----
            record: Log record

        Returns:
        -------
            Message key for rate limiting

        """
        # Use logger name and first 50 characters of message
        message = getattr(record, "getMessage", lambda: str(record.msg))()
        message_pattern = message[:50] if message else ""

        return f"{record.name}:{message_pattern}"

    def _cleanup_old_entries(self, current_time: float) -> None:
        """Remove old entries from message counts.

        Args:
        ----
            current_time: Current timestamp

        """
        keys_to_remove = []

        for key, info in self.message_counts.items():
            if current_time - info["first_seen"] > self.time_window * 2:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.message_counts[key]


# Export filter classes
__all__ = [
    "PerformanceLogFilter",
    "RateLimitFilter",
    "SecurityLogFilter",
    "SensitiveDataFilter",
]
