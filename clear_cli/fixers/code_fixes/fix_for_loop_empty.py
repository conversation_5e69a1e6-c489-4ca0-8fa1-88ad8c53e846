#!/usr/bin/env python3
"""
fix_for_loop_empty.py - Targeted fix for: For loops missing {% empty %}
"""

import argparse
import re
from pathlib import Path


def fix_issue(file_path: str, issue_description: str) -> bool:
    """Apply specific character-level fix to the file."""
    try:
        content = Path(file_path).read_text(encoding="utf-8")
        original_content = content

        # Fix for loops missing {% empty %} fallback
        # Find for loops that don't have {% empty %} and add them

        # Pattern: {% for ... %} ... {% endfor %} without {% empty %}
        for_loop_pattern = r"({% for [^%]+%})(.*?)({% endfor %})"

        def add_empty_fallback(match):
            for_start = match.group(1)
            loop_content = match.group(2)
            for_end = match.group(3)

            # Check if {% empty %} already exists
            if "{% empty %}" in loop_content:
                return match.group(0)  # No change needed

            # Add {% empty %} before {% endfor %}
            return f"{for_start}{loop_content}\n    {{% empty %}}\n        <p>No items available.</p>\n    {for_end}"

        # Apply the fix
        content = re.sub(for_loop_pattern, add_empty_fallback, content, flags=re.DOTALL)

        if content != original_content:
            # Create backup
            backup_path = f"{file_path}.backup"
            Path(backup_path).write_text(original_content, encoding="utf-8")

            # Apply fix
            Path(file_path).write_text(content, encoding="utf-8")
            print(f"SUCCESS: Fixed {issue_description} in {file_path}")
            return True
        else:
            print(f"INFO:  No changes needed for {file_path}")
            return True

    except Exception as e:
        print(f"ERROR: Error fixing {file_path}: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix For loops missing {% empty %}")
    parser.add_argument("--file", required=True, help="File to fix")
    parser.add_argument("--issue", required=True, help="Issue description")

    args = parser.parse_args()

    success = fix_issue(args.file, args.issue)
    exit(0 if success else 1)
