#!/usr/bin/env python3
"""
fix_sr_only.py - Targeted fix for: Uses deprecated 'sr-only' class
"""

import argparse
import re
from pathlib import Path


def fix_issue(file_path: str, issue_description: str) -> bool:
    """Apply specific character-level fix to the file."""
    try:
        content = Path(file_path).read_text(encoding="utf-8")
        original_content = content

        # Fix Bootstrap 4 sr-only to Bootstrap 5 visually-hidden
        content = re.sub(r"\bsr-only\b", "visually-hidden", content)

        if content != original_content:
            # Create backup
            backup_path = f"{file_path}.backup"
            Path(backup_path).write_text(original_content, encoding="utf-8")

            # Apply fix
            Path(file_path).write_text(content, encoding="utf-8")
            print(f"SUCCESS: Fixed {issue_description} in {file_path}")
            return True
        else:
            print(f"INFO:  No changes needed for {file_path}")
            return True

    except Exception as e:
        print(f"ERROR: Error fixing {file_path}: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix Uses deprecated 'sr-only' class")
    parser.add_argument("--file", required=True, help="File to fix")
    parser.add_argument("--issue", required=True, help="Issue description")

    args = parser.parse_args()

    success = fix_issue(args.file, args.issue)
    exit(0 if success else 1)
