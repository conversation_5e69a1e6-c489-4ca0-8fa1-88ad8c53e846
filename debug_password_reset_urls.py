#!/usr/bin/env python
"""
Debug script to check URL resolution for password reset endpoint.
"""

import os
import sys

import django

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")

try:
    django.setup()

    from django.conf import settings
    from django.test import Client
    from django.urls import resolve, reverse

    print("=== Password Reset URL Debug ===")

    # Test URL reverse resolution
    try:
        url = reverse("authentication:password_reset")
        print(f"✓ URL reverse successful: {url}")
    except Exception as e:
        print(f"✗ URL reverse failed: {e}")
        sys.exit(1)

    # Test URL resolution (what view handles this URL)
    try:
        resolver_match = resolve(url)
        print("✓ URL resolves to:")
        print(f"  View: {resolver_match.func}")
        print(
            f"  View class: {resolver_match.func.view_class if hasattr(resolver_match.func, 'view_class') else 'N/A'}"
        )
        print(f"  App name: {resolver_match.app_name}")
        print(f"  URL name: {resolver_match.url_name}")
        print(f"  Namespace: {resolver_match.namespace}")
    except Exception as e:
        print(f"✗ URL resolution failed: {e}")

    # Test the view directly
    print("\n=== Testing View Response ===")
    client = Client()

    try:
        response = client.get(url)
        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            print("✓ Password reset view responds successfully")
            if response.templates:
                print(f"Template used: {response.templates[0].name}")

            # Check if the response contains expected elements
            content = response.content.decode("utf-8", errors="ignore")
            if "password" in content.lower() and "reset" in content.lower():
                print("✓ Response contains password reset content")
            else:
                print("⚠ Response may not contain expected password reset content")

        elif response.status_code == 500:
            print("✗ 500 Internal Server Error")
            # Try to extract error information
            content = response.content.decode("utf-8", errors="ignore")
            if settings.DEBUG and ("Exception" in content or "Traceback" in content):
                print("Error traceback available in response")
                # Look for the actual exception
                lines = content.split("\n")
                in_traceback = False
                for line in lines:
                    if "Exception Type:" in line or "Exception Value:" in line:
                        print(f"  {line.strip()}")
                    elif "Traceback" in line:
                        in_traceback = True
                    elif in_traceback and ("File" in line or "Error" in line):
                        print(f"  {line.strip()}")
                        if "Error" in line:
                            break
        else:
            print(f"✗ Unexpected response status: {response.status_code}")

    except Exception as e:
        print(f"Exception during view test: {e}")
        import traceback

        traceback.print_exc()

    print("\n=== Debug Complete ===")

except Exception as e:
    print(f"Setup error: {e}")
    import traceback

    traceback.print_exc()
