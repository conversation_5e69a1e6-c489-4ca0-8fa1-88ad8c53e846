"""Versioning Signals

Automatically create versions when models are saved with comprehensive
error handling, performance optimization, and proper Django signal patterns.
"""

import contextlib
import logging
import threading
import time
from typing import TYPE_CHECKING, Any, Optional

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import (
    DatabaseError,
    IntegrityError,
    OperationalError,
    models,
    transaction,
)
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

from .models import UniversalVersion, VersionLog

if TYPE_CHECKING:
    from apps.authentication.models import User

User = get_user_model()
logger = logging.getLogger(__name__)

# Thread-local storage for request context
_local = threading.local()


def get_current_request():
    """Get the current request from thread-local storage"""
    return getattr(_local, "request", None)


def set_current_request(request):
    """Set the current request in thread-local storage"""
    _local.request = request


def clear_current_request():
    """Clear the current request from thread-local storage"""
    if hasattr(_local, "request"):
        delattr(_local, "request")


def get_user_from_context(instance: "models.Model") -> Optional["User"]:
    """Get user from various context sources.

    Args:
    ----
        instance: Model instance

    Returns:
    -------
        User object or None

    """
    # Check if user is explicitly set on instance
    user = getattr(instance, "_version_user", None)
    if user and not isinstance(user, AnonymousUser):
        return user

    # Try to get user from current request
    try:
        current_request = get_current_request()
        if current_request and hasattr(current_request, "user") and current_request.user.is_authenticated:
            return current_request.user
    except (ImportError, ModuleNotFoundError):
        pass

    # Try to get user from Django's current user middleware if available
    try:
        from django.contrib.auth import get_user

        request = get_current_request()
        if request:
            user = get_user(request)
            if user and user.is_authenticated:
                return user
    except (ImportError, ModuleNotFoundError):
        pass

    return None


@receiver(pre_save)
def prepare_version_tracking(sender, instance, **kwargs):
    """Prepare version tracking before save.

    This signal tracks the original state before changes for better
    diff generation and change detection.
    """
    from .mixins import VersionedModelMixin

    # Only process versioned models
    if not isinstance(instance, VersionedModelMixin):
        return

    # Skip if versioning is disabled for this instance
    if not getattr(instance, "auto_version", True):
        return

    # Skip if this is during version creation to avoid recursion
    if getattr(instance, "_creating_version", False):
        return

    try:
        # Store original state for change detection
        if instance.pk:
            # Get the original instance from database
            try:
                original = sender.objects.get(pk=instance.pk)
                instance._original_state = original.serialize_for_version()
            except ObjectDoesNotExist:
                instance._original_state = None
        else:
            instance._original_state = None

    except (DatabaseError, IntegrityError, OperationalError) as e:
        logger.warning(f"Failed to prepare version tracking for {instance}: {e}")


@receiver(post_save)
def create_version_on_save(sender, instance, created: bool, **kwargs):
    """Automatically create versions for models that inherit from VersionedModelMixin.

    This is the main signal that triggers version creation with comprehensive
    error handling and performance optimization.
    """
    from .mixins import VersionedModelMixin

    # Only process versioned models
    if not isinstance(instance, VersionedModelMixin):
        return

    # Skip if auto_version is disabled
    if not getattr(instance, "auto_version", True):
        return

    # Skip if this is during version creation to avoid infinite recursion
    if getattr(instance, "_creating_version", False):
        return

    # Skip if explicitly disabled for this save
    if getattr(instance, "_skip_version", False):
        return

    # Get user from context
    user = get_user_from_context(instance)
    if not user:
        logger.debug(f"No user found for versioning {instance}")
        return

    # Track processing time for performance monitoring
    start_time = time.time()

    try:
        # Set flag to prevent recursion
        instance._creating_version = True

        # Check if this is a significant change
        is_significant = getattr(instance, "_force_significant", None)
        if is_significant is None:
            is_significant = _is_significant_change(instance, created)

        # Get summary from context or generate default
        summary = getattr(instance, "_version_summary", None)
        branch_name = getattr(instance, "_version_branch", None)

        # Prepare metadata
        metadata = {
            "auto_created": True,
            "is_new_object": created,
            "processing_time_ms": None,  # Will be set after creation
        }

        # Add custom metadata if provided
        custom_metadata = getattr(instance, "_version_metadata", None)
        if custom_metadata:
            metadata.update(custom_metadata)

        # Create version within transaction
        with transaction.atomic():
            version = instance.create_version(
                summary=summary,
                user=user,
                branch_name=branch_name,
                is_significant=is_significant,
                metadata=metadata,
            )

            # Update processing time
            processing_time_ms = int((time.time() - start_time) * 1000)

            # Update the version's metadata with processing time
            if version and version.metadata:
                version.metadata["processing_time_ms"] = processing_time_ms
                version.save(update_fields=["metadata"])

            # Log performance if it's slow
            if processing_time_ms > 1000:  # More than 1 second
                logger.warning(f"Slow version creation for {instance}: {processing_time_ms}ms")

    except (ValidationError, ValueError) as e:
        # Log error but don't break the save process
        processing_time_ms = int((time.time() - start_time) * 1000)
        logger.error(
            f"Failed to create version for {instance} after {processing_time_ms}ms: {e}",
            extra={
                "model": instance._meta.label,
                "instance_pk": instance.pk,
                "created": created,
                "processing_time_ms": processing_time_ms,
            },
        )
    finally:
        # Clean up temporary attributes
        for attr in [
            "_creating_version",
            "_version_user",
            "_version_summary",
            "_version_branch",
            "_version_metadata",
            "_force_significant",
            "_skip_version",
            "_original_state",
        ]:
            if hasattr(instance, attr):
                delattr(instance, attr)


def _is_significant_change(instance: "models.Model", is_new: bool) -> bool:
    """Determine if the change is significant enough to mark the version as such.

    Args:
    ----
        instance: Model instance
        is_new: Whether this is a new object

    Returns:
    -------
        True if change is significant

    """
    # New objects are always significant
    if is_new:
        return True

    # Check if instance has custom significance detection
    if hasattr(instance, "_is_version_significant"):
        try:
            current_data = instance.serialize_for_version()
            return instance._is_version_significant(current_data)
        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error in custom significance detection: {e}")

    # Check if any significant fields changed
    if hasattr(instance, "get_significant_fields"):
        try:
            significant_fields = instance.get_significant_fields()
            original_state = getattr(instance, "_original_state", {})
            current_state = instance.serialize_for_version()

            for field in significant_fields:
                if original_state.get(field) != current_state.get(field):
                    return True

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error checking significant fields: {e}")

    # Default to significant if we can't determine
    return True


@receiver(post_delete)
def log_deletion(sender, instance, **kwargs):
    """Log when versioned models are deleted.

    Creates a comprehensive log entry for audit trail purposes.
    """
    from .mixins import VersionedModelMixin
    from .models import VersionLog

    # Only process versioned models
    if not isinstance(instance, VersionedModelMixin):
        return

    # Get user from context
    user = get_user_from_context(instance)
    if not user:
        logger.debug(f"No user found for deletion logging of {instance}")
        return

    try:
        # Get the latest version for reference
        latest_version = None
        with contextlib.suppress(Exception):
            latest_version = instance.get_latest_version()

        # Prepare deletion metadata
        metadata = {
            "deleted_at": timezone.now().isoformat(),
            "model_str": str(instance),
            "had_versions": (instance.has_versions() if hasattr(instance, "has_versions") else False),
            "last_version_number": (latest_version.version_number if latest_version else None),
        }

        # Add model-specific metadata
        if hasattr(instance, "updated_at"):
            metadata["last_updated"] = instance.updated_at.isoformat()

        # Create deletion log entry
        VersionLog.objects.create(
            log_name=instance.get_log_name(),
            model_name=instance._meta.model_name,
            object_id=instance.pk,
            user=user,
            action="deleted",
            summary=f"Deleted {instance._meta.verbose_name}: {instance!s}",
            version=latest_version,
            metadata=metadata,
        )

        logger.info(
            f"Logged deletion of {instance._meta.label} {instance.pk} by {user}",
            extra={
                "model": instance._meta.label,
                "instance_pk": instance.pk,
                "user_id": user.pk,
                "had_versions": metadata["had_versions"],
            },
        )

    except (AttributeError, KeyError, ValueError, TypeError) as e:
        logger.error(
            f"Failed to log deletion for {instance}: {e}",
            extra={
                "model": instance._meta.label,
                "instance_pk": instance.pk,
                "user_id": user.pk if user else None,
            },
        )


class VersioningMiddleware:
    """Middleware to provide user context for automatic versioning.

    This middleware stores the current request in thread-local storage
    so that signals can access user information for version creation.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        """Process request and store in thread-local storage"""
        # Store request in thread-local storage
        set_current_request(request)

        try:
            response = self.get_response(request)
        finally:
            # Always clean up thread-local storage
            clear_current_request()

        return response

    def process_exception(self, request, exception):
        """Clean up on exception"""
        clear_current_request()


class VersionContextManager:
    """Context manager for setting versioning parameters.

    Usage:
        with VersionContextManager(user=request.user, summary="Custom summary"):
            instance.save()
    """

    def __init__(
        self,
        user: Optional["User"] = None,
        summary: str | None = None,
        branch: str | None = None,
        significant: bool | None = None,
        skip_version: bool = False,
        metadata: dict[str, Any] | None = None,
    ):
        """Initialize context manager.

        Args:
        ----
            user: User to attribute versions to
            summary: Custom version summary
            branch: Branch name for version
            significant: Force version significance
            skip_version: Skip version creation entirely
            metadata: Additional metadata for version

        """
        self.user = user
        self.summary = summary
        self.branch = branch
        self.significant = significant
        self.skip_version = skip_version
        self.metadata = metadata or {}
        self._original_request = None

    def __enter__(self):
        """Set up versioning context"""
        # Store original request if we're setting a user
        if self.user:
            self._original_request = get_current_request()

            # Create a mock request with the user
            class MockRequest:
                def __init__(self, user):
                    self.user = user

            set_current_request(MockRequest(self.user))

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up versioning context"""
        # Restore original request
        if self._original_request is not None:
            set_current_request(self._original_request)
        elif self.user:
            clear_current_request()

    def save_with_version(self, instance, **save_kwargs):
        """Save an instance with versioning context.

        Args:
        ----
            instance: Model instance to save
            **save_kwargs: Additional arguments for save()

        """
        # Set versioning attributes on instance
        if self.user:
            instance._version_user = self.user
        if self.summary:
            instance._version_summary = self.summary
        if self.branch:
            instance._version_branch = self.branch
        if self.significant is not None:
            instance._force_significant = self.significant
        if self.skip_version:
            instance._skip_version = True
        if self.metadata:
            instance._version_metadata = self.metadata

        # Save the instance
        instance.save(**save_kwargs)


def bulk_create_versions(instances: list, user: "User", summary: str | None = None, batch_size: int = 100):
    """Create versions for multiple instances efficiently.

    Args:
    ----
        instances: List of model instances
        user: User to attribute versions to
        summary: Summary for all versions
        batch_size: Number of instances to process per batch

    """
    from django.contrib.contenttypes.models import ContentType

    from .models import UniversalVersion, VersionLog

    versions_to_create = []
    logs_to_create = []

    for i, instance in enumerate(instances):
        try:
            # Skip if not a versioned model
            if not hasattr(instance, "serialize_for_version"):
                continue

            # Get content type
            content_type = ContentType.objects.get_for_model(instance)

            # Get next version number
            next_version = instance.get_next_version_number()

            # Serialize data
            serialized_data = instance.serialize_for_version()

            # Create version object (don't save yet)
            version = UniversalVersion(
                content_type=content_type,
                object_id=instance.pk,
                version_number=next_version,
                created_by=user,
                change_summary=summary or "Bulk version creation",
                serialized_data=serialized_data,
                is_significant=True,
                metadata={
                    "bulk_created": True,
                    "batch_index": i,
                },
            )
            versions_to_create.append(version)

            # Create log object (don't save yet)
            log = VersionLog(
                log_name=instance.get_log_name(),
                model_name=instance._meta.model_name,
                object_id=instance.pk,
                user=user,
                action="created",
                summary=summary or "Bulk version creation",
                metadata={
                    "bulk_created": True,
                    "batch_index": i,
                },
            )
            logs_to_create.append((log, version))

            # Process in batches
            if len(versions_to_create) >= batch_size:
                _create_version_batch(versions_to_create, logs_to_create)
                versions_to_create.clear()
                logs_to_create.clear()

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Failed to prepare bulk version for {instance}: {e}")

    # Process remaining instances
    if versions_to_create:
        _create_version_batch(versions_to_create, logs_to_create)


def _create_version_batch(versions: list, logs_with_versions: list):
    """Create a batch of versions and logs efficiently"""
    try:
        with transaction.atomic():
            # Bulk create versions
            created_versions = UniversalVersion.objects.bulk_create(versions)

            # Update logs with version references and create
            logs_to_create = []
            for (log, _version), created_version in zip(logs_with_versions, created_versions, strict=False):
                log.version = created_version
                logs_to_create.append(log)

            # Bulk create logs
            VersionLog.objects.bulk_create(logs_to_create)

    except Exception as e:
        logger.error(f"Failed to create version batch: {e}")
        raise
