"""Management command to find potential duplicate persons in the database

from django.db import models
from django.db.models import Q, QuerySet
from rest_framework import authentication
from rest_framework import status
from typing import Any
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time

This command analyzes Person records to identify potential duplicates based on:
- Name similarity (fuzzy matching)
- Email address matching
- Company affiliation analysis
- Organization context

Supports organization filtering and configurable similarity thresholds.
"""

import logging
from typing import Any

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q

from apps.authentication.models import Organization
from apps.projects.models import Person

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Find potential duplicate Person records within organizations"""

    help = "Find potential duplicate persons based on name, email, and company similarity"

    def add_arguments(self, parser) -> None:
        parser.add_argument(
            "--threshold",
            type=int,
            default=85,
            help="Name similarity threshold percentage (default: 85)",
        )
        parser.add_argument("--organization-id", type=int, help="Filter by specific organization ID")
        parser.add_argument("--organization-slug", type=str, help="Filter by organization slug")
        parser.add_argument(
            "--email-only",
            action="store_true",
            help="Only check for exact email duplicates",
        )
        parser.add_argument(
            "--include-inactive",
            action="store_true",
            help="Include inactive persons in analysis",
        )
        parser.add_argument("--export-csv", type=str, help="Export results to CSV file")

    def handle(self, *args, **options) -> None:
        """Execute the duplicate detection command"""
        try:
            self._setup_command(options)
            persons = self._get_persons(options)
            duplicates = self._find_duplicates(persons, options)
            self._display_results(duplicates, persons.count(), options)

            if options.get("export_csv"):
                self._export_to_csv(duplicates, options["export_csv"])

        except CommandError:
            raise
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Unexpected error: {e!s}"))
            logger.exception("Error in find_duplicate_persons command")

    def _setup_command(self, options: dict[str, Any]) -> None:
        """Validate options and setup command execution"""
        threshold = options["threshold"]
        if not 50 <= threshold <= 100:
            raise CommandError("Threshold must be between 50 and 100")

        # Try to import fuzzy matching library
        try:
            global fuzz
            from fuzzywuzzy import fuzz
        except ImportError:
            if not options["email_only"]:
                raise CommandError(
                    "fuzzywuzzy library required for name similarity matching. "
                    "Install with: pip install fuzzywuzzy[speedup] or use --email-only",
                )

    def _get_persons(self, options: dict[str, Any]) -> "QuerySet[Person]":
        """Get filtered Person queryset based on options"""
        persons = Person.objects.select_related("organization")

        # Filter by organization
        if options.get("organization_id"):
            try:
                org = Organization.objects.get(id=options["organization_id"])
                persons = persons.filter(organization=org)
                self.stdout.write(f"Filtering by organization: {org.name} (ID: {org.id})")
            except Organization.DoesNotExist:
                raise CommandError(f"Organization with ID {options['organization_id']} not found")

        elif options.get("organization_slug"):
            try:
                org = Organization.objects.get(slug=options["organization_slug"])
                persons = persons.filter(organization=org)
                self.stdout.write(f"Filtering by organization: {org.name} (slug: {org.slug})")
            except Organization.DoesNotExist:
                raise CommandError(f'Organization with slug "{options["organization_slug"]}" not found')

        # Filter by active status
        if not options.get("include_inactive"):
            persons = persons.filter(is_active=True)

        return persons.order_by("organization", "first_name", "last_name")

    def _find_duplicates(self, persons: "QuerySet[Person]", options: dict[str, Any]) -> list[dict[str, Any]]:
        """Find potential duplicate persons"""
        duplicates = []
        threshold = options["threshold"]
        email_only = options.get("email_only", False)

        self.stdout.write(f"Analyzing {persons.count()} persons for duplicates...")
        self.stdout.write(f"Using similarity threshold: {threshold}%")
        if email_only:
            self.stdout.write("Email-only mode: checking exact email matches")
        self.stdout.write("")

        persons_list = list(persons)
        checked_pairs = set()

        for i, person1 in enumerate(persons_list):
            # Show progress for large datasets
            if i % 100 == 0 and i > 0:
                self.stdout.write(f"Processed {i} persons...")

            for person2 in persons_list[i + 1 :]:
                # Skip if different organizations (persons should be in same org)
                if person1.organization_id != person2.organization_id:
                    continue

                # Skip if already checked this pair
                pair_key = tuple(sorted([person1.id, person2.id]))
                if pair_key in checked_pairs:
                    continue
                checked_pairs.add(pair_key)

                duplicate_info = self._check_duplicate_pair(person1, person2, threshold, email_only)
                if duplicate_info:
                    duplicates.append(duplicate_info)

        return duplicates

    def _check_duplicate_pair(
        self,
        person1: Person,
        person2: Person,
        threshold: int,
        email_only: bool,
    ) -> dict[str, Any] | None:
        """Check if two persons are potential duplicates"""
        reasons = []
        name_similarity = 0
        email_similarity = 0

        # Check email exact match
        if person1.email and person2.email and person1.email.lower() == person2.email.lower():
            reasons.append("Identical email")
            email_similarity = 100

        if email_only:
            # In email-only mode, only flag exact email matches
            if reasons:
                return {
                    "person1": person1,
                    "person2": person2,
                    "reasons": reasons,
                    "name_similarity": 0,
                    "email_similarity": email_similarity,
                    "confidence": "High" if email_similarity == 100 else "Low",
                }
            return None

        # Name similarity analysis
        full_name1 = f"{person1.first_name} {person1.last_name}".strip().lower()
        full_name2 = f"{person2.first_name} {person2.last_name}".strip().lower()

        if full_name1 and full_name2:
            name_similarity = fuzz.ratio(full_name1, full_name2)

            if name_similarity >= threshold:
                reasons.append(f"Name similarity: {name_similarity}%")

        # Company similarity for name matches
        if name_similarity >= 70:  # Lower threshold for company check
            if person1.company and person2.company:
                company_similarity = fuzz.ratio(person1.company.lower(), person2.company.lower())
                if company_similarity >= 80:
                    reasons.append(f"Same company ({company_similarity}% match)")

        # Phone number matching
        if self._phones_match(person1, person2):
            reasons.append("Matching phone number")

        # Return duplicate info if we found reasons
        if reasons:
            confidence = "High" if (name_similarity >= 90 or email_similarity == 100) else "Medium"
            return {
                "person1": person1,
                "person2": person2,
                "reasons": reasons,
                "name_similarity": name_similarity,
                "email_similarity": email_similarity,
                "confidence": confidence,
            }

        return None

    def _phones_match(self, person1: Person, person2: Person) -> bool:
        """Check if persons have matching phone numbers"""
        phones1 = {p for p in [person1.phone] if p}
        phones2 = {p for p in [person2.phone] if p}

        # Normalize phone numbers (remove non-digits)
        phones1_normalized = {"".join(filter(str.isdigit, p)) for p in phones1}
        phones2_normalized = {"".join(filter(str.isdigit, p)) for p in phones2}

        return bool(phones1_normalized & phones2_normalized)

    def _display_results(
        self,
        duplicates: list[dict[str, Any]],
        total_persons: int,
        options: dict[str, Any],
    ) -> None:
        """Display analysis results"""
        if duplicates:
            self.stdout.write(self.style.WARNING(f"\nFound {len(duplicates)} potential duplicates:\n"))

            for i, dup in enumerate(duplicates, 1):
                self._display_duplicate_pair(i, dup)

            self._display_summary(duplicates, total_persons)
        else:
            self.stdout.write(self.style.SUCCESS("No potential duplicates found!"))

        # Additional analysis
        self._analyze_data_quality(options)

    def _display_duplicate_pair(self, index: int, dup: dict[str, Any]) -> None:
        """Display information about a duplicate pair"""
        p1 = dup["person1"]
        p2 = dup["person2"]

        self.stdout.write(
            f"{index}. Potential duplicate ({', '.join(dup['reasons'])}) - {dup['confidence']} confidence:",
        )

        for label, person in [("Person 1", p1), ("Person 2", p2)]:
            self.stdout.write(f"   {label}: {person.first_name} {person.last_name} (ID: {person.id})")
            self.stdout.write(f"      - Email: {person.email or 'None'}")
            self.stdout.write(f"      - Phone: {person.phone or 'None'}")
            self.stdout.write(f"      - Company: {person.company or 'None'}")
            self.stdout.write(f"      - Organization: {person.organization.name if person.organization else 'None'}")
            self.stdout.write(f"      - Active: {person.is_active}")
            self.stdout.write(f"      - Created: {person.created_at.strftime('%Y-%m-%d %H:%M')}")

        self.stdout.write("")

    def _display_summary(self, duplicates: list[dict[str, Any]], total_persons: int) -> None:
        """Display summary statistics"""
        self.stdout.write(self.style.SUCCESS("\nSummary:"))
        self.stdout.write(f"Total persons analyzed: {total_persons}")
        self.stdout.write(f"Potential duplicates found: {len(duplicates)}")

        # Group by confidence level
        high_confidence = len([d for d in duplicates if d["confidence"] == "High"])
        medium_confidence = len([d for d in duplicates if d["confidence"] == "Medium"])

        self.stdout.write(f"  - High confidence: {high_confidence}")
        self.stdout.write(f"  - Medium confidence: {medium_confidence}")

        # Group by reason type
        by_email = len([d for d in duplicates if any("email" in r.lower() for r in d["reasons"])])
        by_name = len([d for d in duplicates if any("name" in r.lower() for r in d["reasons"])])
        by_phone = len([d for d in duplicates if any("phone" in r.lower() for r in d["reasons"])])

        self.stdout.write(f"  - By email match: {by_email}")
        self.stdout.write(f"  - By name similarity: {by_name}")
        self.stdout.write(f"  - By phone match: {by_phone}")

    def _analyze_data_quality(self, options: dict[str, Any]) -> None:
        """Analyze overall data quality"""
        if options.get("organization_id") or options.get("organization_slug"):
            # If filtering by organization, get that organization's persons
            if options.get("organization_id"):
                persons = Person.objects.filter(organization_id=options["organization_id"])
            else:
                persons = Person.objects.filter(organization__slug=options["organization_slug"])
        else:
            persons = Person.objects.all()

        if not options.get("include_inactive"):
            persons = persons.filter(is_active=True)

        total = persons.count()
        if total == 0:
            return

        self.stdout.write(self.style.WARNING("\nData Quality Analysis:"))

        missing_email = persons.filter(Q(email__isnull=True) | Q(email="")).count()
        missing_phone = persons.filter(Q(phone__isnull=True) | Q(phone="")).count()
        missing_company = persons.filter(Q(company__isnull=True) | Q(company="")).count()
        missing_first_name = persons.filter(Q(first_name__isnull=True) | Q(first_name="")).count()
        missing_last_name = persons.filter(Q(last_name__isnull=True) | Q(last_name="")).count()

        self.stdout.write(f"  - Missing email: {missing_email} ({missing_email / total * 100:.1f}%)")
        self.stdout.write(f"  - Missing phone: {missing_phone} ({missing_phone / total * 100:.1f}%)")
        self.stdout.write(f"  - Missing company: {missing_company} ({missing_company / total * 100:.1f}%)")
        self.stdout.write(f"  - Missing first name: {missing_first_name} ({missing_first_name / total * 100:.1f}%)")
        self.stdout.write(f"  - Missing last name: {missing_last_name} ({missing_last_name / total * 100:.1f}%)")

    def _export_to_csv(self, duplicates: list[dict[str, Any]], filename: str) -> None:
        """Export duplicate results to CSV file"""
        import csv

        try:
            with open(filename, "w", newline="", encoding="utf-8") as csvfile:
                fieldnames = [
                    "person1_id",
                    "person1_name",
                    "person1_email",
                    "person1_company",
                    "person2_id",
                    "person2_name",
                    "person2_email",
                    "person2_company",
                    "reasons",
                    "confidence",
                    "name_similarity",
                    "email_similarity",
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for dup in duplicates:
                    p1 = dup["person1"]
                    p2 = dup["person2"]

                    writer.writerow(
                        {
                            "person1_id": p1.id,
                            "person1_name": f"{p1.first_name} {p1.last_name}",
                            "person1_email": p1.email or "",
                            "person1_company": p1.company or "",
                            "person2_id": p2.id,
                            "person2_name": f"{p2.first_name} {p2.last_name}",
                            "person2_email": p2.email or "",
                            "person2_company": p2.company or "",
                            "reasons": "; ".join(dup["reasons"]),
                            "confidence": dup["confidence"],
                            "name_similarity": dup["name_similarity"],
                            "email_similarity": dup["email_similarity"],
                        },
                    )

            self.stdout.write(self.style.SUCCESS(f"Results exported to: {filename}"))

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stdout.write(self.style.ERROR(f"Failed to export CSV: {e!s}"))
